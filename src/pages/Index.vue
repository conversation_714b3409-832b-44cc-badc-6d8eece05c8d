<template>
  <q-page class="flex justify-center items-center bg-grey-2">
    <!-- 外层容器，设置为flex并启用align-items: stretch -->
    <div class="flex q-pa-lg rounded-borders" style="width: 100%; display: flex; align-items: stretch;">
      <!-- 左边布局：vSpeaker -->
      <div class="flex column q-pa-lg bg-white shadow-2 rounded-borders" style="width: 49%; margin-right: 2%;">
        <span class="text-h5 q-mb-md text-primary">vSpeaker</span>

        <!-- Path In -->
        <div class="q-mb-md">
          <span class="text-subtitle1 q-mb-sm text-grey-8">Path In</span>
          <q-icon name="mic" />
          <!-- <q-icon name="mic" /> -->
          <q-list dense bordered class="rounded-borders">
            <q-item v-for="(item, index) in speakerPathInOptions" :key="index" :class="{ 'disabled-item': !canClick }" :clickable="canClick" v-ripple @click="selectPathIn(item.id)" class="q-my-xs">
              <q-item-section avatar>
                <q-icon v-if="selectedPathIn === item.id" name="check" color="primary" />
              </q-item-section>
              <q-item-section class="text-grey-9">{{ item.name }}</q-item-section>
            </q-item>
          </q-list>
        </div>

        <!-- Path Out -->
        <div>
          <span class="text-subtitle1 q-mb-sm text-grey-8">Path Out</span>
          <q-icon name="volume_up" />
          <q-list dense bordered class="rounded-borders">
            <q-item v-for="(item, index) in speakerPathOutOptions" :key="index" :class="{ 'disabled-item': true }" :clickable="false" v-ripple @click="selectPathOut(item.id)" class="q-my-xs">
              <q-item-section avatar>
                <q-icon v-if="selectedPathOut === item.id" name="check" color="primary" />
              </q-item-section>
              <q-item-section class="text-grey-9">{{ item.name }}</q-item-section>
            </q-item>
          </q-list>
        </div>
      </div>

      <!-- 右边布局：vMic -->
      <div class="flex column q-pa-lg bg-white shadow-2 rounded-borders" style="width: 49%;">
        <span class="text-h5 q-mb-md text-primary">vMic</span>

        <!-- Path In -->
        <div class="q-mb-md">
          <span class="text-subtitle1 q-mb-sm text-grey-8">Path In</span>
          <q-icon name="mic" />
          <q-list dense bordered class="rounded-borders">
            <q-item v-for="(item, index) in micPathInOptions" :key="index" :class="{ 'disabled-item': !canClick }" :clickable="canClick" v-ripple @click="selectMicPathIn(item.id)" class="q-my-xs">
              <q-item-section avatar>
                <q-icon v-if="selectedMicPathIn === item.id" name="check" color="primary" />
              </q-item-section>
              <q-item-section class="text-grey-9">{{ item.name }}</q-item-section>
            </q-item>
          </q-list>
        </div>

        <!-- Path Out -->
        <div>
          <span class="text-subtitle1 q-mb-sm text-grey-8">Path Out</span>
          <q-icon name="volume_up" />
          <q-list dense bordered class="rounded-borders">
            <q-item v-for="(item, index) in micPathOutOptions" :key="index" :class="{ 'disabled-item': true }" :clickable="false" v-ripple @click="selectMicPathOut(item.id)" class="q-my-xs">
              <q-item-section avatar>
                <q-icon v-if="selectedMicPathOut === item.id" name="check" color="primary" />
              </q-item-section>
              <q-item-section class="text-grey-9">{{ item.name }}</q-item-section>
            </q-item>
          </q-list>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script>
const { ipcRenderer } = require('electron')
export default {
  name: 'PageIndex',
  data() {
    return {
      speakerPathInOptions: [],
      speakerPathOutOptions: [],
      micPathInOptions: [],
      micPathOutOptions: [],
      selectedPathIn: null, // 选中的 Path In
      selectedPathOut: null, // 选中的 Path Out
      selectedMicPathIn: null, // 选中的 Mic Path In
      selectedMicPathOut: null, // 选中的 Mic Path Out
      selectedInputDevice: 0,
      selectedOutputDevice: 0,
      canClick: true
    }
  },
  methods: {
    //speaker 输入点击
    selectPathIn(index) {
      this.selectedPathIn = index;
      ipcRenderer.send('setSystemOutputDevice',{index})
    },
    //speaker 输出点击
    selectPathOut(index) {
      this.selectedPathOut = index;
      ipcRenderer.send('setSystemOutputDevice',{index})
    },
    //mic 输入点击
    selectMicPathIn(index) {
      this.selectedMicPathIn = index;
      ipcRenderer.send('setSystemInputDevice',{index})
    },
    //mic 输出点击
    selectMicPathOut(index) {
      this.selectedMicPathOut = index;
      ipcRenderer.send('setSystemInputDevice',{index})
    },
    checkVspeakerOutput(id){
      console.log('speakerPathOutOptions ',this.speakerPathOutOptions)
      this.speakerPathOutOptions.forEach(item=>{
        if(item.id == id && item.name.includes('GTEAudio')){
          this.selectedPathOut = 0
        }
      })
    },
    //同步系统的输入输出
    async syncSystemDevice() {
      // const devices = await navigator.mediaDevices.enumerateDevices();
      let devicesInfo = await ipcRenderer.invoke('syncSystemDevice',null)
      
      this.speakerPathInOptions = []
      this.speakerPathOutOptions = []
      this.micPathInOptions = []
      this.micPathOutOptions = []
      console.log('devicesInfo ', devicesInfo)
      
      this.speakerPathInOptions = devicesInfo.audioOutputDevices
      this.speakerPathOutOptions = devicesInfo.audioOutputDevices

      this.micPathInOptions = devicesInfo.audioInputDevices
      this.micPathOutOptions = devicesInfo.audioInputDevices

      this.selectedInputDevice = devicesInfo.selectedInputDevice
      this.selectedOutputDevice = devicesInfo.selectedOutputDevice

      this.selectedPathIn = this.selectedOutputDevice
      this.selectedPathOut = this.selectedOutputDevice
      this.checkVspeakerOutput(this.selectedPathOut)
      this.selectedMicPathIn = this.selectedInputDevice
      this.selectedMicPathOut = this.selectedInputDevice
    },
  },
  async mounted() {
    this.syncSystemDevice()
    ipcRenderer.on('vSpeakerInfo',(event, info) =>{
      //[ 'GTEAudio 2ch', 'Green Terp Audio Device' ]
      console.log('vSpeakerInfo ',info)
      this.canClick = false
      this.speakerPathOutOptions.forEach(item=>{
        console.log(item.name,info.input)
        if(item.name.includes('UI Sounds')){
          
        }
        if(item.name.includes(info.input)){
          console.log('selectedPathIn ',item.name,info.input)
          this.selectedPathIn = item.id
        }
        //解决windows上，GTEAudio 输入和输出名字不一样
        if(info.input.includes('GTEAudio')){
          if(item.name.includes('GTEAudio')){
            console.log('selectedPathIn ',item.name,info.input)
            this.selectedPathIn = item.id
          }
        }
      })
      this.speakerPathOutOptions.forEach(item=>{
        
        if(item.name.includes('UI Sounds')){
          
        }else if(item.name.includes(info.output)){
          console.log('selectedPathOut ',item.name,info.output)
          this.selectedPathOut = item.id
          this.checkVspeakerOutput(this.selectedPathOut)
        }
      })

    })
    ipcRenderer.on('vMicInfo',(event,info)=>{
      this.canClick = false
      console.log('vMicInfo ',info)
      this.canClick = false
      this.micPathInOptions.forEach(item=>{
        console.log('MIC IN ',item.name,'===',info.input)
        if(item.name.includes('UI Sounds')){
          
        }if(item.name.includes(info.input)){
          this.selectedMicPathIn = item.id
        }
      })
      this.micPathInOptions.forEach(item=>{
        console.log('MIC OUT ',item.name,'===',info.output)
        if(item.name.includes('UI Sounds')){
          
        }else if(item.name.includes(info.output)){
          this.selectedMicPathOut = item.id
        }
         //解决windows上，GTEAudio 输入和输出名字不一样
        if(info.output.includes('GTEAudio')){
          if(item.name.includes('GTEAudio')){
            this.selectedMicPathOut = item.id
          }
        }
      })
    })
    ipcRenderer.on('useSystem',async ()=>{
      this.canClick = true
      let devicesInfo = await ipcRenderer.invoke('syncSystemDevice',null)
      console.log('syncSystemDevice ',devicesInfo)
      this.selectedInputDevice = devicesInfo.selectedInputDevice
      this.selectedOutputDevice = devicesInfo.selectedOutputDevice
      
      this.selectedPathIn = this.selectedOutputDevice
      this.selectedPathOut = this.selectedOutputDevice
      this.checkVspeakerOutput(this.selectedPathOut)
      this.selectedMicPathIn = this.selectedInputDevice
      this.selectedMicPathOut = this.selectedInputDevice
    })

    navigator.mediaDevices.ondevicechange = async (event) => {
      console.log("设备发生变化，重新枚举设备...");
      const devices = await navigator.mediaDevices.enumerateDevices();
      this.syncSystemDevice()
      ipcRenderer.send("ondevicechange");
    };
  },
  destroyed() {
  }
}
</script>

<style lang="scss" scoped>
.q-page {
  padding: 2px;
  overflow-y: hidden; // 禁止整体页面垂直滚动
}

.q-list {
  border: 1px solid #e0e0e0;
  max-height: 200px; // 设置列表的最大高度
  overflow-y: auto; // 允许列表垂直滚动
}

.q-item {
  transition: background-color 0.3s ease;
}

.q-item:hover {
  background-color: #f5f5f5;
}

.q-item-section {
  padding: 8px 0;
}

.text-primary {
  color: #1976d2;
}

.text-grey-8 {
  color: #424242;
}

.text-grey-9 {
  color: #616161;
}

.shadow-2 {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.rounded-borders {
  border-radius: 8px;
}
.disabled-item {
  opacity: 0.5; // 降低透明度
  pointer-events: none; // 禁用鼠标事件
  cursor: not-allowed; // 更改鼠标样式为不可点击
}
</style>