<template>
  <div id="q-app">
    <router-view />
    <q-dialog v-model="alert">
      <q-card> 
        <q-card-section>
          <div class="text-h6">Progress</div>
        </q-card-section>

        <q-card-section class="q-pt-none process">
          <q-linear-progress :value="progress" class="q-mt-md" />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="OK" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script>
import { ipc<PERSON>enderer } from 'electron'
export default {
  name: 'App',
  data() {
    return {
      alert: false,
      progress: 0
    }
  },
  mounted() {
    ipcRenderer.on("message", (event, message) => {
      console.log('message ',message);
      let index = message.index
      if(index == 2){
        //正在下载
        //  this.$q.dialog({
        //     title: this.$t('alert'),
        //     message: this.$t('updateAvailable'),
        //     cancel: false,
        //     persistent: true
        //   })
        this.$q.dialog({
          title: this.$t('confirm'),
          message: this.$t('updateAvailable'),
          cancel: true,
          persistent: true
        }).onOk(() => {
          ipcRenderer.send("startUpdate");
        }).onCancel(() => {
          ipcRenderer.send("closeWindow");
        })
      }else if(index == 3){
        //暂无更新
          this.$q.dialog({
            title: this.$t('alert'),
            message: this.$t('updateNotAvailable'),
            cancel: false,
            persistent: true
          }).onDismiss(() => {
            ipcRenderer.send("closeWindow");
          })
      }
    });
    ipcRenderer.on("downloadProgress", (event, progressObj) => {
      this.alert = true
      this.progress =  progressObj.percent / 100
      console.log('downloadProgress ',progressObj.percent);
    });
    ipcRenderer.on("isUpdateNow", () => {
      console.log('isUpdateNow')
      this.alert = false
      this.$q.dialog({
        title: this.$t('confirm'),
        message: this.$t('updateNow'),
        cancel: true,
        persistent: true
      }).onOk(() => {
        ipcRenderer.send("isUpdateNow");
      })
      
    });
  },
}
</script>
<style lang="scss" scoped>
  #q-app {
    width: 100vw;
    height: 100vh;
  }
  .process{
    width: 350px;
  }
  </style>
  