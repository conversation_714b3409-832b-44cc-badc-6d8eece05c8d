import { app, BrowserWindow, nativeTheme, globalShortcut, ipcMain, Menu, Tray } from 'electron'
import { autoUpdater } from "electron-updater"
const log = require("electron-log")
const cp = require('child_process')
// const iconv = require('iconv-lite'); // 已移除未使用的依赖

const path = require('path')
const { exec,execSync,spawn } = require('child_process');
const os = require('os');
const audioDevices = require('@spotxyz/macos-audio-devices');
import audioDeviceWrapper from "./audioDeviceWrapper"
// import net from 'net'
// const HOST = '127.0.0.1'
// const PORT = 53868
// const server = net.createServer()
// // 记录客户端列表
// let clientMap = new Map();
// server.listen(PORT, HOST)

let systemAudioDevice
try {
  if (process.platform === 'win32' && nativeTheme.shouldUseDarkColors === true) {
    require('fs').unlinkSync(require('path').join(app.getPath('userData'), 'DevTools Extensions'))
  }
} catch (_) { }

// let audioOutputDevices = audioDevices.getOutputDevices.sync();
// let audioInputDevices = audioDevices.getInputDevices.sync();
// let selectedInputDevice = audioDevices.getDefaultInputDevice.sync().id;
// let selectedOutputDevice = audioDevices.getDefaultOutputDevice.sync().id;
let audioOutputDevices,audioInputDevices,selectedInputDevice,selectedOutputDevice
/**
 * Set `__statics` path to static files in production;
 * The reason we are setting it here is that the path needs to be evaluated at runtime
 */
if (process.env.PROD) {
  global.__statics = __dirname
}
const assetsDirectory = path.join(__statics, '')
let mainWindow
let tray = null

ipcMain.handle('syncSystemDevice', async (event, info) => {
    audioOutputDevices = await systemAudioDevice.getSystemOutputDevices()
    audioInputDevices = await systemAudioDevice.getSystemInputDevices()
    selectedInputDevice = await systemAudioDevice.getDefaultInputAudioDevice()
    selectedOutputDevice = await systemAudioDevice.getDefaultOutputAudioDevice()
    selectedInputDevice = selectedInputDevice.id
    selectedOutputDevice = selectedOutputDevice.id
    // console.log({ audioOutputDevices, audioInputDevices, selectedInputDevice, selectedOutputDevice })
    return { audioOutputDevices, audioInputDevices, selectedInputDevice, selectedOutputDevice }
})
ipcMain.on('setSystemOutputDevice', (event, info) => {
  let id = info.index
  systemAudioDevice.setOutputDevice(id)
})
ipcMain.on('setSystemInputDevice', (event, info) => {
  let id = info.index
  systemAudioDevice.setInputDevice(id);
})

ipcMain.on('ondevicechange', (e, arg) => {
  console.log('ondevicechange ')
})
// 优化的进程检查函数，减少 Windows 下的 CPU 占用
function checkProcess(processName) {
  return new Promise((resolve, reject) => {
    const isWindows = os.platform() === 'win32';
    let command;

    if (isWindows) {
      // 使用更轻量的 tasklist 命令替代 PowerShell
      command = `tasklist /FI "IMAGENAME eq ${processName}.exe" /FO CSV /NH`;
    } else {
      // macOS/Linux 下使用 ps + grep
      command = `ps -ef | grep ${processName} | grep -v grep`;
    }

    exec(command, {
      encoding: 'utf8',
      timeout: 3000 // 添加超时限制
    }, (error, stdout, stderr) => {
      if (error || stderr) return resolve(null);

      if (isWindows) {
        // Windows tasklist 输出格式检查
        const hasProcess = stdout.includes(`"${processName}.exe"`);
        if (hasProcess) {
          // 如果进程存在，再获取详细命令行信息
          const detailCommand = `wmic process where "name='${processName}.exe'" get CommandLine /format:value`;
          exec(detailCommand, { encoding: 'utf8', timeout: 3000 }, (detailError, detailStdout) => {
            if (detailError) return resolve(processName); // 至少返回进程名
            const commandLine = detailStdout.match(/CommandLine=(.*)/);
            resolve(commandLine ? commandLine[1].trim() : processName);
          });
        } else {
          resolve(null);
        }
      } else {
        resolve(stdout.includes(processName) ? stdout.trim() : null);
      }
    });
  });
}


// 优化扫描间隔，减少 CPU 占用
let lastScanTime = 0;
const SCAN_INTERVAL = 8000; // 增加到8秒，减少扫描频率

setInterval(() => {
  const now = Date.now();
  if (now - lastScanTime >= SCAN_INTERVAL) {
    checkSoxProcess();
    lastScanTime = now;
  }
}, SCAN_INTERVAL);
//处理interpreteast
function dealInterpreteastSox(order) {
  let matches = [];
  let regex = /-t coreaudio\s+([^-]+)/g;
  if (process.platform != 'darwin') {
    regex = /-t waveaudio "([^"]+)"/g;
  }
  let match;
  while ((match = regex.exec(order)) !== null) {
    matches.push(match[1].trim());
  }
  console.log(matches);
  let input = matches[0]
  let output = matches[1]
  mainWindow.webContents.send('vSpeakerInfo',{input,output})
}
//处理pitch bass
function dealPitchAndBass(order){
  let regex = /-t coreaudio\s+([^-]+)/g;
  if (process.platform != 'darwin') {
    regex = /-t waveaudio "([^"]+)"/g;
  }
  const matches = [];
  let match;
  while ((match = regex.exec(order)) !== null) {
    matches.push(match[1].trim());
  }
  console.log(matches);
  let input = matches[0]
  let output = matches[1].split('pitch')[0].trimEnd()
  if(input.includes('GTEAudio')){
    //命令输入是GTE，扬声器通路
    mainWindow.webContents.send('vSpeakerInfo',{input,output})
  }else if(output.includes('GTEAudio')){
    mainWindow.webContents.send('vMicInfo',{input,output})
  }
}

//处理dealEnhancer
function dealEnhancer(command){
  // if (process.platform == 'darwin') {
    let chrisSoundDevice
    if (process.platform == 'darwin') {
      chrisSoundDevice = process.env.PROD ?  path.join(__statics,'../../lib/sd-mac/ChrisSoundDevice') : path.join(app.getAppPath(),'../../lib/sd-mac/ChrisSoundDevice')
    }else{
      chrisSoundDevice = process.env.PROD ?  path.join(__statics,'../../lib/sd-win/ChrisSoundDevice.exe') : path.join(app.getAppPath(),'../../lib/sd-win/ChrisSoundDevice.exe')
    }
    const sounddeviceInfo = execSync(chrisSoundDevice, { encoding: 'utf8' });
    let sounddeviceObject = null
    try {
        sounddeviceObject = JSON.parse(sounddeviceInfo);
        // console.log('Parsed object:', sounddeviceObject);
    } catch (error) {
        console.error('Failed to parse sounddeviceInfo as JSON:', error.message);
    }
    const regex = /(?:--in |-in )(\d+)|(?:--out |-out )(\d+)/g;
    let match;
    let input;
    let output;
    let inputDevice;
    let outputDevice;
    // 循环查找所有匹配项
    while ((match = regex.exec(command)) !== null) {
        if (match[1]) {
            input = parseInt(match[1], 10);
        } else if (match[2]) {
            output = parseInt(match[2], 10);
        }
    }
    console.log('input ',input,' output ',output)
    //判断输入是输入设备还是输出设备
    sounddeviceObject.forEach(device => {
      if(device.index == input){
        inputDevice = device
      }
      if(device.index == output){
        outputDevice = device
      }
    });
    console.log('inputDevice ',inputDevice,' outputDevice ',outputDevice)
    
    if(inputDevice.name.includes('GTE')){
      if (process.platform == 'darwin') {
        mainWindow.webContents.send('vSpeakerInfo',{input:inputDevice.name,output:outputDevice.name})
      }else{
        //windows sounddevice 在调整默认输出后，列表index会变化
        mainWindow.webContents.send('vSpeakerInfo',{input:inputDevice.name,output:null})
      }
      
    }else{
      mainWindow.webContents.send('vMicInfo',{input:inputDevice.name,output:outputDevice.name})
    }
    // if(inputDevice.name.includes('GTE')){
    //   mainWindow.webContents.send('vSpeakerInfo',{input:inputDevice.name,output:outputDevice.name})
    // }else{
    //   mainWindow.webContents.send('vMicInfo',{input:inputDevice.name,output:outputDevice.name})
    // }
  // }
  
}

let last = 'system'
let lastProcessStates = new Map(); // 记录上次进程状态

async function checkSoxProcess() {
  let useSystemDevice = true

  try {
    // 检查 gtassist 进程
    const gtassistOutput = await checkProcess("gtassist");
    if (gtassistOutput) {
      currentState = 'interpreteast'
      // 只有状态变化或命令行变化时才处理
      if (last !== 'interpreteast' || lastProcessStates.get('gtassist') !== gtassistOutput) {
        console.log('gtassist process exist:', gtassistOutput);
        last = 'interpreteast'
        lastProcessStates.set('gtassist', gtassistOutput);
        dealInterpreteastSox(gtassistOutput);
      }
      useSystemDevice = false
    } else {
      lastProcessStates.delete('gtassist');
    }
  } catch (err) {
    console.error('检查 gtassist 进程时发生错误：', err);
  }

  try {
    // 检查 gtpb 进程
    const gtpbOutput = await checkProcess("gtpb");
    if (gtpbOutput) {
      currentState = 'gtpb'
      // 只有状态变化或命令行变化时才处理
      if (last !== 'gtpb' || lastProcessStates.get('gtpb') !== gtpbOutput) {
        console.log('gtpb process exist:', gtpbOutput);
        last = 'gtpb'
        lastProcessStates.set('gtpb', gtpbOutput);
        dealPitchAndBass(gtpbOutput)
      }
      useSystemDevice = false
    } else {
      lastProcessStates.delete('gtpb');
    }
  } catch (err) {
    console.error('检查 gtpb 进程时发生错误：', err);
  }

  try {
    // 检查 gtenhancer 进程
    const gtenhancerOutput = await checkProcess("gtenhancer");
    if (gtenhancerOutput) {
      if(gtenhancerOutput.includes('-sd 2')) return

      currentState = 'gtenhancer'
      // 只有状态变化或命令行变化时才处理
      if (last !== 'gtenhancer' || lastProcessStates.get('gtenhancer') !== gtenhancerOutput) {
        console.log('gtenhancer process exist:', gtenhancerOutput);
        last = 'gtenhancer'
        lastProcessStates.set('gtenhancer', gtenhancerOutput);
        dealEnhancer(gtenhancerOutput)
      }
      useSystemDevice = false
    } else {
      lastProcessStates.delete('gtenhancer');
    }
  } catch (err) {
    console.error('检查 gtenhancer 进程时发生错误：', err);
  }

  // 只有状态真正变化时才发送系统设备消息
  if(useSystemDevice && last !== 'system'){
    console.log('切换到系统设备');
    last = 'system'
    mainWindow.webContents.send('useSystem')
  }
}

function createWindow() {
  
  if (mainWindow != null) {
    // mainWindow.show()
    return
  }
  mainWindow = new BrowserWindow({
    width: 900,
    height: 600,
    useContentSize: true,
    show: true,
    webPreferences: {
      // Change from /quasar.conf.js > electron > nodeIntegration;
      // More info: https://quasar.dev/quasar-cli/developing-electron-apps/node-integration
      nodeIntegration: process.env.QUASAR_NODE_INTEGRATION,
      nodeIntegrationInWorker: process.env.QUASAR_NODE_INTEGRATION,
      contextIsolation: false

      // More info: /quasar-cli/developing-electron-apps/electron-preload-script
      // preload: path.resolve(__dirname, 'electron-preload.js')
    }
  })
  systemAudioDevice = new audioDeviceWrapper(mainWindow)
  systemAudioDevice.start()
  mainWindow.loadURL(process.env.APP_URL)
  mainWindow.setMenu(null)
  if (process.env.DEBUGGING) {
    mainWindow.webContents.openDevTools()
  }
  mainWindow.on('show', () => {
  })
  mainWindow.on('closed', () => {
    mainWindow = null
  })
  // createTray()
}

function createTray() {
  // 加载托盘图标
  var iconPath = null
  if (process.platform == 'darwin') {
    iconPath = path.join(assetsDirectory, 'gtTemplate-color.png')
  } else {
    iconPath = path.resolve(__statics, 'gtTemplate-color.png')
  }
  console.log('iconPath ', iconPath)
  // 创建托盘实例
  tray = new Tray(iconPath);

  // 创建右键菜单
  updateTrayMenu();

  // 点击托盘图标时显示/隐藏主窗口
  tray.on('click', () => {
    if (mainWindow) {
      if (mainWindow.isVisible()) {
        // mainWindow.hide();
      } else {
        // mainWindow.show();
      }
    }
  });
}

function updateTrayMenu() {
  // 生成 Audio Input Device 菜单项
  const inputDeviceMenuItems = audioInputDevices.map((device) => {
    const isGTEAudio2ch = device.name === 'GTEAudio 2ch';
    const isOutputDeviceGTEAudio2ch = selectedOutputDevice === device.id && isGTEAudio2ch;

    return {
      label: (selectedInputDevice === device.id ? '✓  ' : '     ') + device.name, // 手动添加选中标记
      type: 'normal', // 使用普通菜单项
      enabled: !isOutputDeviceGTEAudio2ch, // 如果输出设备是 GTEAudio 2ch，则输入设备的 GTEAudio 2ch 置灰
      click: () => {
        selectedInputDevice = device.id;
        console.log('选中的输入设备:', device.name);
        audioDevices.setDefaultInputDevice(device.id);
        updateTrayMenu();
      },
    };
  });

  // 生成 Audio Output Device 菜单项
  const outputDeviceMenuItems = audioOutputDevices.map((device) => {
    const isGTEAudio2ch = device.name === 'GTEAudio 2ch';
    const isInputDeviceGTEAudio2ch = selectedInputDevice === device.id && isGTEAudio2ch;

    return {
      label: (selectedOutputDevice === device.id ? '✓  ' : '     ') + device.name, // 手动添加选中标记
      type: 'normal', // 使用普通菜单项
      enabled: !isInputDeviceGTEAudio2ch, // 如果输入设备是 GTEAudio 2ch，则输出设备的 GTEAudio 2ch 置灰
      click: () => {
        selectedOutputDevice = device.id;
        console.log('选中的输出设备:', device.name);
        audioDevices.setDefaultOutputDevice(device.id);
        updateTrayMenu();
      },
    };
  });

  // 更新托盘菜单
  // audio path input, audio path output
  const contextMenu = Menu.buildFromTemplate([
    {
      label: 'vMic Path In/Out',
      enabled: false,
    },
    ...inputDeviceMenuItems,
    { type: 'separator' },
    {
      label: 'vSpeaker Path In/Out',
      enabled: false,
    },
    ...outputDeviceMenuItems,
    { type: 'separator' },
    {
      label: 'Quit',
      click: () => {
        app.quit();
      },
    },
  ]);

  // 设置托盘菜单
  tray.setContextMenu(contextMenu);
}

app.on('ready', () => {
  createWindow()
})

app.whenReady().then(() => {
  const template = [
    {
      label: 'Edit',
      submenu: [{ role: 'undo' }, { role: 'redo' }, { type: 'separator' }, { role: 'cut' }, { role: 'copy' }, { role: 'paste' }, { role: 'pasteandmatchstyle' }, { role: 'delete' }, { role: 'selectall' }]
    }
  ]
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        {
          role: 'quit'
        }
      ]
    })
    const menu = Menu.buildFromTemplate(template)
    if (!process.env.DEBUGGING) {
      Menu.setApplicationMenu(menu)
    }
  }
})

app.on('window-all-closed', () => {
  // if (process.platform !== 'darwin') {
    app.quit()
  // }
})

app.on('will-quit', () => {
})


app.on('activate', () => {
  if (mainWindow === null) {
    createWindow()
  }
})
app.allowRendererProcessReuse = false;