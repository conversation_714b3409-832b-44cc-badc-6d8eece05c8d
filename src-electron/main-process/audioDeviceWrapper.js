const { spawn, exec } = require('child_process');
import { ipcMain, app, BrowserWindow} from 'electron'
const audioDevices = require('@spotxyz/macos-audio-devices');
import { setDefaultOutputDevice,setDefaultInputDevice } from '@spotxyz/macos-audio-devices'
import path from 'path'
export default class audioDeviceWrapper {

    constructor(mainWindow) {
        this.mainWindow = mainWindow;
    }

    start(){
        this.SoundVolumeView = process.env.PROD ? path.join(__statics, '../../lib/SoundVolumeView.exe') :
        path.join(app.getAppPath(), '../../lib/SoundVolumeView.exe')
        console.log('SoundVolumeView ',this.SoundVolumeView)
        this.GetNir = process.env.PROD ? path.join(__statics, '../../lib/GetNir.exe') :
        path.join(app.getAppPath(), '../../lib/GetNir.exe')
    }

    setOutputDevice(output, type = 'multimedia') {
        if (process.platform == 'darwin') {
            console.log('mac set as default ',output)
            setDefaultOutputDevice(output)
        }else{
            const DefaultTypes = {
                all: 'all',
                multimedia: 1,
                communications: 2,
            };
            const id = this.getValidId(output);
            if (['all', 'multimedia', 'communications'].includes(type) === false) {
                throw new Error('invalid default type: ' + type);
            }
            const defaultType = DefaultTypes[type];
            const soundVolumeViewPath = `"${this.SoundVolumeView}"`;
            const order = `${soundVolumeViewPath} /SetDefault ${id} ${defaultType}`
            exec(order, (error, stdout, stderr) => {
    
            })
        }
    }

    setInputDevice(input, type = 'multimedia') {
        if (process.platform == 'darwin') {
            console.log('mac set as default ',input)
            setDefaultInputDevice(input)
        }else{
            const DefaultTypes = {
                all: 'all',
                multimedia: 1,
                communications: 2,
            };
            const id = this.getValidId(input);
            if (['all', 'multimedia', 'communications'].includes(type) === false) {
                throw new Error('invalid default type: ' + type);
            }
            const defaultType = DefaultTypes[type];
            const soundVolumeViewPath = `"${this.SoundVolumeView}"`;
            const order = `${soundVolumeViewPath} /SetDefault ${id} ${defaultType}`
            console.log('order ',order)
            exec(order, (error, stdout, stderr) => {
    
            })
        }
    }
    

    getValidId(output) {
        const id = typeof output === 'string' ? output : output.id;
        if (!id || typeof id !== 'string' || id.length === 0) {
            throw new Error('invalid output id: ' + id);
        }
        return id;
    }

    //获取默认输入设备
    getDefaultInputAudioDevice(){
        let defaultInputDevice 
        if (process.platform == 'darwin') {
            defaultInputDevice = audioDevices.getDefaultInputDevice.sync()
        }else{
            defaultInputDevice = this.getWinDefaultInputAudioDevice()
        }
        return defaultInputDevice
    }
    
    //获取默认输出设备
    getDefaultOutputAudioDevice(){
        let defaultOutputDevice 
        if (process.platform == 'darwin') {
            defaultOutputDevice = audioDevices.getDefaultOutputDevice.sync()
        }else{
            defaultOutputDevice = this.getWinDefaultOutputAudioDevice()
        }
        return defaultOutputDevice
    }

    //获取系统输入设备列表
    getSystemInputDevices(){
        let inputDevices
        if (process.platform == 'darwin') {
            inputDevices = audioDevices.getInputDevices.sync();
        }else{
            inputDevices = this.getWinSystemInputDevice()
        }
        return inputDevices
    }

    //获取系统输出设备列表
    getSystemOutputDevices(){
        let outputDevices
        if (process.platform == 'darwin') {
            outputDevices = audioDevices.getOutputDevices.sync();
        }else{
            outputDevices = this.getWinSystemOutputDevice()
        }
        return outputDevices
    }

    async getWinDefaultInputAudioDevice(){
        let inputDevices = await this.getWinSystemInputDevice()
        let defaultInputDevice = null
        inputDevices.forEach(element => {
            if(element.isDefaultMultimedia){
                defaultInputDevice = element
            }
        });
        return defaultInputDevice
    }

    async getWinDefaultOutputAudioDevice(){
        let outputDevices = await this.getWinSystemOutputDevice()
        let defaultOutputDevice = null
        outputDevices.forEach(element => {
            if(element.isDefaultMultimedia){
                defaultOutputDevice = element
            }
        });
        return defaultOutputDevice
    }

    getWinSystemInputDevice() {
        // audio device
        return new Promise((resolve, reject) => {
            // 使用 SoundVolumeView 和 GetNir 命令获取音频输入设备信息
            const soundVolumeViewPath = `"${this.SoundVolumeView}"`;
            const getNirPath = `"${this.GetNir}"`;
            const command = `${soundVolumeViewPath} /stab "" | ${getNirPath} "Item ID, Name, Device Name, Default Multimedia, Default Communications, Muted, Volume Percent" "Type=Device && Direction=Capture && DeviceState=Active"`;
            exec(command, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                    return;
                }
                // 解析命令输出
                const devices = [];
                const lines = stdout.split('\r\n');
                for (const line of lines) {
                    const [id, name, deviceName, isDefaultMultimedia, isDefaultCommunications, isMuted, volume] = line.split('\t');
                    devices.push({
                        id,
                        name: name+' (' + deviceName+')',
                        deviceName,
                        fullName: name+' (' + deviceName+')',
                        isDefaultMultimedia: isDefaultMultimedia === 'Capture',
                        isDefaultCommunications: isDefaultCommunications === 'Capture',
                        isMuted: isMuted === 'Yes',
                        volume: Number(volume.replace('%', '')),
                    });
                }
    
                resolve(devices);
            });
        });
    }
        

    getWinSystemOutputDevice(){
        //audio device
        return new Promise((resolve, reject) => {
            // 使用 SoundVolumeView 和 GetNir 命令获取音频输出设备信息
            const soundVolumeViewPath = `"${this.SoundVolumeView}"`;
            const getNirPath = `"${this.GetNir}"`;
            // const command = this.SoundVolumeView + ' /stab "" | ' + this.GetNir + ' "Item ID, Name, Device Name, Default Multimedia, Default Communications, Muted, Volume Percent" "Type=Device && Direction=Render && DeviceState=Active"';
            const command = `${soundVolumeViewPath} /stab "" | ${getNirPath} "Item ID, Name, Device Name, Default Multimedia, Default Communications, Muted, Volume Percent" "Type=Device && Direction=Render && DeviceState=Active"`;
            exec(command, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                    return;
                }
                // 解析命令输出
                const devices = [];
                const lines = stdout.split('\r\n');
                for (const line of lines) {
                    const [id, name, deviceName, isDefaultMultimedia, isDefaultCommunications, isMuted, volume] = line.split('\t');
                    devices.push({
                        id,
                        name:name+' (' + deviceName+')',
                        deviceName,
                        fullName: name+' (' + deviceName+')',
                        isDefaultMultimedia: isDefaultMultimedia === 'Render',
                        isDefaultCommunications: isDefaultCommunications === 'Render',
                        isMuted: isMuted === 'Yes',
                        volume: Number(volume.replace('%', '')),
                    });
                }

                resolve(devices);
            });
        });
    }

}
