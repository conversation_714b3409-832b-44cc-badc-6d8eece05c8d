{"name": "GTEController", "version": "0.0.6", "description": "GTE Controller", "productName": "GTE Controller", "author": "Green Terp Technologies Pte Ltd", "private": true, "scripts": {"test": "echo \"No test specified\" && exit 0"}, "dependencies": {"@electron/notarize": "^2.5.0", "@quasar/extras": "^1.0.0", "@spotxyz/macos-audio-devices": "^1.5.0", "applescript": "^1.0.0", "axios": "^0.21.1", "core-js": "^3.6.5", "electron-log": "^4.4.8", "electron-updater": "^5.2.1", "iconv-lite": "^0.6.3", "quasar": "^1.0.0", "vue-i18n": "^8.0.0"}, "devDependencies": {"@quasar/app": "^2.0.0", "devtron": "^1.4.0", "electron": "^12.0.0", "electron-builder": "^22.4.0", "electron-debug": "^3.0.1", "electron-devtools-installer": "^3.0.0", "electron-rebuild": "^3.2.9", "node-sass": "^7.0.3", "sass-loader": "^10.1.1"}, "browserslist": ["last 10 Chrome versions", "last 10 Firefox versions", "last 4 Edge versions", "last 7 Safari versions", "last 8 Android versions", "last 8 ChromeAndroid versions", "last 8 FirefoxAndroid versions", "last 10 iOS versions", "last 5 Opera versions"], "engines": {"node": ">= 10.18.1", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}